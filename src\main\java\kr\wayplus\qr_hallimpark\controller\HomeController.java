package kr.wayplus.qr_hallimpark.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 홈 페이지 컨트롤러
 * - 메인 페이지 및 기본 라우팅 처리
 */
@Controller
public class HomeController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 메인 홈 페이지
     */
    @GetMapping("/")
    public String home(Model model) {
        logger.debug("Home page requested");
        
        // 페이지 메타 정보 설정
        model.addAttribute("pageTitle", "홈");
        model.addAttribute("pageDescription", "한림공원에서 QR 코드를 스캔하여 다양한 퀴즈와 게임을 즐겨보세요.");
        
        // 사용자 인증 정보 확인
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        boolean isAuthenticated = auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser");
        
        if (isAuthenticated) {
            logger.debug("Authenticated user accessing home: {}", auth.getName());
            model.addAttribute("isAuthenticated", true);
            model.addAttribute("username", auth.getName());
        } else {
            logger.debug("Anonymous user accessing home");
            model.addAttribute("isAuthenticated", false);
        }
        
        return "index";
    }

    /**
     * 퀴즈 페이지 (임시)
     */
    @GetMapping("/quiz")
    public String quiz(Model model) {
        logger.debug("Quiz page requested");
        
        model.addAttribute("pageTitle", "퀴즈");
        model.addAttribute("pageDescription", "다양한 퀴즈를 풀어보세요.");
        
        return "quiz/index";
    }

    /**
     * 스토리 페이지 (임시)
     */
    @GetMapping("/story")
    public String story(Model model) {
        logger.debug("Story page requested");
        
        model.addAttribute("pageTitle", "스토리");
        model.addAttribute("pageDescription", "흥미진진한 스토리를 따라가세요.");
        
        return "story/index";
    }

    /**
     * 지도 페이지 (임시)
     */
    @GetMapping("/map")
    public String map(Model model) {
        logger.debug("Map page requested");
        
        model.addAttribute("pageTitle", "지도");
        model.addAttribute("pageDescription", "한림공원 지도를 확인하세요.");
        
        return "map/index";
    }

    /**
     * 도움말 페이지 (임시)
     */
    @GetMapping("/help")
    public String help(Model model) {
        logger.debug("Help page requested");
        
        model.addAttribute("pageTitle", "이용 안내");
        model.addAttribute("pageDescription", "한림공원 QR 체험 서비스 이용 방법을 안내합니다.");
        
        return "help/index";
    }

    /**
     * 문의 페이지 (임시)
     */
    @GetMapping("/contact")
    public String contact(Model model) {
        logger.debug("Contact page requested");
        
        model.addAttribute("pageTitle", "문의하기");
        model.addAttribute("pageDescription", "궁금한 사항이나 문제가 있으시면 언제든 문의해주세요.");
        
        return "contact/index";
    }

    /**
     * FAQ 페이지 (임시)
     */
    @GetMapping("/faq")
    public String faq(Model model) {
        logger.debug("FAQ page requested");

        model.addAttribute("pageTitle", "자주 묻는 질문");
        model.addAttribute("pageDescription", "자주 묻는 질문과 답변을 확인하세요.");

        return "faq/index";
    }

    /**
     * 마이페이지 (로그인 필요)
     */
    @GetMapping("/mypage")
    public String mypage(Model model) {
        logger.debug("Mypage requested");

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        model.addAttribute("pageTitle", "마이페이지");
        model.addAttribute("pageDescription", "나의 활동 이력과 정보를 확인하세요.");
        model.addAttribute("username", auth.getName());

        return "mypage/index";
    }
}
