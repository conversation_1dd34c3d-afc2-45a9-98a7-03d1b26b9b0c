<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/default}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>접근 권한 없음 - 한림공원 QR 체험</title>
    <meta name="description" content="해당 페이지에 접근할 권한이 없습니다.">
</head>
<body>
    <div layout:fragment="content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="text-center">
                        <!-- 에러 아이콘 -->
                        <div class="mb-4">
                            <i class="fas fa-lock text-warning" style="font-size: 6rem;"></i>
                        </div>
                        
                        <!-- 에러 코드 -->
                        <h1 class="display-1 fw-bold text-warning mb-3">403</h1>
                        
                        <!-- 에러 메시지 -->
                        <h2 class="h3 mb-3">접근 권한이 없습니다</h2>
                        <p class="lead text-muted mb-4">
                            해당 페이지에 접근할 권한이 없습니다.<br>
                            로그인이 필요하거나 관리자 권한이 필요할 수 있습니다.
                        </p>
                        
                        <!-- 요청 URL 표시 (개발 환경에서만) -->
                        <div class="alert alert-light border" th:if="${requestUrl != null}">
                            <small class="text-muted">
                                <strong>요청 URL:</strong> <span th:text="${requestUrl}"></span>
                            </small>
                        </div>
                        
                        <!-- 로그인 상태에 따른 다른 메시지 -->
                        <div sec:authorize="!isAuthenticated()" class="alert alert-info border-0 mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>로그인이 필요합니다
                            </h6>
                            <p class="mb-0">
                                해당 페이지를 이용하시려면 먼저 로그인해주세요.
                            </p>
                        </div>
                        
                        <div sec:authorize="isAuthenticated()" class="alert alert-warning border-0 mb-4">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>권한이 부족합니다
                            </h6>
                            <p class="mb-0">
                                현재 계정으로는 해당 페이지에 접근할 수 없습니다. 
                                관리자에게 문의해주세요.
                            </p>
                        </div>
                        
                        <!-- 액션 버튼들 -->
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mb-5">
                            <!-- 비로그인 사용자용 버튼 -->
                            <div sec:authorize="!isAuthenticated()">
                                <a th:href="@{/user/login}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>로그인하기
                                </a>
                            </div>
                            <a th:href="@{/}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-home me-2"></i>홈으로 돌아가기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
