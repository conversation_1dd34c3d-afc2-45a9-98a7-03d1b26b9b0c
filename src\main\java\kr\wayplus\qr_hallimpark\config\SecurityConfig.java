package kr.wayplus.qr_hallimpark.config;

import kr.wayplus.qr_hallimpark.common.utils.CustomBcryptPasswordEncoder;
import kr.wayplus.qr_hallimpark.config.handler.LoginFailureHandler;
import kr.wayplus.qr_hallimpark.config.handler.LoginSuccessHandler;
import kr.wayplus.qr_hallimpark.config.handler.SecurityAccessDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
	@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")

	@Autowired
	LoginSuccessHandler loginSuccessHandler;

	@Autowired
	LoginFailureHandler loginFailureHandler;

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new CustomBcryptPasswordEncoder();
	}

	@Bean
	public SecurityFilterChain webFilterChain(HttpSecurity http) throws Exception {

		http.authorizeRequests()
			.requestMatchers("/manage/**").access("hasAnyRole('ADMIN', 'MANAGER')")
			.requestMatchers("/**").permitAll();
		http.formLogin()
				.loginPage("/user/login")
				.loginProcessingUrl("/user/login-progress")
				.usernameParameter("user-email")
				.passwordParameter("user-pass")
				.successHandler(loginSuccessHandler)
				.failureHandler(loginFailureHandler);
		http.logout()
				.logoutUrl("/user/logout")
				.invalidateHttpSession(true)
				.deleteCookies("JSESSIONID");
		http.exceptionHandling()
				.accessDeniedHandler(new SecurityAccessDeniedHandler());
		http.cors(
			(cors) ->
				cors.configurationSource(corsConfigurationSource())
				);
		http.sessionManagement()
				.sessionCreationPolicy(SessionCreationPolicy.ALWAYS)
				.maximumSessions(1)
				.expiredUrl("/user/session-expired");

		return http.build();
	}




	@Bean
	public CorsConfigurationSource corsConfigurationSource(){
		CorsConfiguration navigatorApiConfiguration = new CorsConfiguration();
		navigatorApiConfiguration.addAllowedOriginPattern("*");
		navigatorApiConfiguration.addAllowedOrigin("Accept-*");
		navigatorApiConfiguration.addAllowedMethod(HttpMethod.POST);
		navigatorApiConfiguration.addAllowedMethod(HttpMethod.GET);
		navigatorApiConfiguration.addAllowedHeader("*");
		navigatorApiConfiguration.addAllowedHeader("Origin");
		navigatorApiConfiguration.setAllowCredentials(true);

		UrlBasedCorsConfigurationSource configurationSource = new UrlBasedCorsConfigurationSource();
		configurationSource.registerCorsConfiguration("/api/**" , navigatorApiConfiguration);
		return configurationSource;
	}
}
