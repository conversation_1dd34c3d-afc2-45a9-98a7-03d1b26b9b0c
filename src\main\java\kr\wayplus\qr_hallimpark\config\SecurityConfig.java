package kr.wayplus.qr_hallimpark.config;

import kr.wayplus.qr_hallimpark.common.utils.CustomBcryptPasswordEncoder;
import kr.wayplus.qr_hallimpark.config.handler.LoginFailureHandler;
import kr.wayplus.qr_hallimpark.config.handler.LoginSuccessHandler;
import kr.wayplus.qr_hallimpark.config.handler.SecurityAccessDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
	@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")

	@Autowired
	LoginSuccessHandler loginSuccessHandler;

	@Autowired
	LoginFailureHandler loginFailureHandler;

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new CustomBcryptPasswordEncoder();
	}

	@Bean
	public SecurityFilterChain webFilterChain(HttpSecurity http) throws Exception {

		http.authorizeHttpRequests(authz -> authz
			.requestMatchers("/manage/**").hasAnyRole("ADMIN", "MANAGER")
			.requestMatchers("/user/login", "/user/session-expired", "/").permitAll()
			.requestMatchers("/css/**", "/js/**", "/images/**", "/favicon.ico", "/error/**").permitAll()
			.anyRequest().authenticated()
		);

		http.formLogin(form -> form
			.loginPage("/user/login")
			.loginProcessingUrl("/user/login-progress")
			.usernameParameter("user-email")
			.passwordParameter("user-pass")
			.successHandler(loginSuccessHandler)
			.failureHandler(loginFailureHandler)
			.permitAll()
		);

		http.logout(logout -> logout
			.logoutUrl("/user/logout")
			.invalidateHttpSession(true)
			.deleteCookies("JSESSIONID")
			.permitAll()
		);

		http.exceptionHandling(exceptions -> exceptions
			.accessDeniedHandler(new SecurityAccessDeniedHandler())
		);

		http.cors(cors -> cors
			.configurationSource(corsConfigurationSource())
		);

		http.sessionManagement(session -> session
			.sessionCreationPolicy(SessionCreationPolicy.ALWAYS)
			.maximumSessions(1)
			.expiredUrl("/user/session-expired")
		);

		// CSRF 설정 (기본적으로 활성화됨)
		http.csrf(csrf -> csrf
			.ignoringRequestMatchers("/api/**") // API 요청은 CSRF 제외
		);

		return http.build();
	}




	@Bean
	public CorsConfigurationSource corsConfigurationSource(){
		CorsConfiguration navigatorApiConfiguration = new CorsConfiguration();
		navigatorApiConfiguration.addAllowedOriginPattern("*");
		navigatorApiConfiguration.addAllowedOrigin("Accept-*");
		navigatorApiConfiguration.addAllowedMethod(HttpMethod.POST);
		navigatorApiConfiguration.addAllowedMethod(HttpMethod.GET);
		navigatorApiConfiguration.addAllowedHeader("*");
		navigatorApiConfiguration.addAllowedHeader("Origin");
		navigatorApiConfiguration.setAllowCredentials(true);

		UrlBasedCorsConfigurationSource configurationSource = new UrlBasedCorsConfigurationSource();
		configurationSource.registerCorsConfiguration("/api/**" , navigatorApiConfiguration);
		return configurationSource;
	}
}
