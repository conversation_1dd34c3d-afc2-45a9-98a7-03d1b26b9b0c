spring:
  profiles:
    group:
      "dev": "default, dev"
      "server": "default, server"
  thymeleaf:
    cache: false
    check-template-location: true
    prefix: classpath:/templates/
    suffix: .html
  devtools:
    livereload:
      enabled: true
  freemarker:
    cache: false
---
spring:
  config:
    activate:
      on-profile:
      - default
server:
  error:
    whitelabel:
      enabled: true
  compression:
    mime-types:
     -text/html
     -text/xml
     -text/plain
     -text/css
     -text/javascript
     -application/javascript    
     -application/json
  tomcat:
    uri-encoding: UTF-8  
mybatis:
  type-aliases-package: kr.wayplus.qr_hallimpark.model
  configuration:
    default-statement-timeout: 30
    auto-mapping-unknown-column-behavior: warning
  mapper-locations: classpath:/sqlmapper/
cookie-set:
  domain: "qr-hallimpark.co.kr"
  prefix: "qr-hallimpark.co.kr"
  tracking: true
  tracking-day: 365
api-keys:
  kakao:
    rest: "205b64d59ec2c0b2cb8abccace50dc80"
    javascript: "adcbde2783f9be83b647223a08a183cd"
  publicdata:
    devel: "XfHf4tp71NaQDWEicDUcAF9o%2BOLxfUYBqmsFJqlBJ5C7nCtLiQyZE7Mswq9bAAfPKP6qvWoeWC1iIq8nirP6yg%3D%3D"
    service: "XfHf4tp71NaQDWEicDUcAF9o%2BOLxfUYBqmsFJqlBJ5C7nCtLiQyZE7Mswq9bAAfPKP6qvWoeWC1iIq8nirP6yg%3D%3D"
using:
  spring:
    schedulerFactory: true
trace: false

---
spring:
  config:
    activate:
      on-profile: "dev"
  datasource:
    jdbc-url: **********************************************************************************
    username: qr_hallimpark
    password: dnpdlvmffjtmQRHanpark00^^
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
server:
  port: 8082
  error:
    include-stacktrace: never
upload:
  path:
    file: "c:/logs/upload/qr_hallimpark/file/"
    image: "c:/logs/upload/qr_hallimpark/image/"
  image:
    max-size: 10485760
  file:
    max-size: 104857600

debug: true
---
spring:
  config:
    activate:
      on-profile: "server"
  datasource:
    jdbc-url: **********************************************************************************
    username: qr_hallimpark
    password: qr_hallimpark
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
server:
  port: 80
  error:
    include-stacktrace: never
upload:
  path:
    file: "/usr/local/tomcat/external_upload/qr_hallimpark/file/"
    image: "/usr/local/tomcat/external_upload/qr_hallimpark/image/"
  image:
    max-size: 10485760
  file:
    max-size: 104857600

debug: false
