/* 한림공원 QR 체험 - 공통 스타일 */

/* 기본 설정 */
:root {
    --primary-color: #2E8B57;
    --secondary-color: #20B2AA;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Noto Sans KR', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #ffffff;
}

/* 헤더 스타일 */
.header {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--dark-color) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* 메인 콘텐츠 */
.main-content {
    min-height: calc(100vh - 200px);
    padding-top: 20px;
}

/* 푸터 스타일 */
.footer {
    margin-top: auto;
}

.social-links a {
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* 에러 페이지 스타일 */
.error-page {
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.error-icon {
    font-size: 6rem;
    margin-bottom: 2rem;
}

.error-code {
    font-size: 8rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 1rem;
}

.error-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.error-description {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

/* 카드 스타일 */
.card {
    border: none;
    border-radius: 12px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* 버튼 스타일 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #236B47;
    border-color: #236B47;
    transform: translateY(-2px);
}

.btn-floating {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 알림 스타일 */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .error-code {
        font-size: 6rem;
    }
    
    .error-title {
        font-size: 1.5rem;
    }
    
    .error-description {
        font-size: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .error-code {
        font-size: 4rem;
    }
    
    .error-icon {
        font-size: 4rem;
    }
    
    .main-content {
        padding-top: 10px;
    }
}

/* 애니메이션 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* 스크롤바 스타일 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #236B47;
}

/* 포커스 스타일 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
    border-color: var(--primary-color);
}

/* 로딩 스피너 */
.spinner-border-custom {
    color: var(--primary-color);
}

/* 텍스트 유틸리티 */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

/* 그림자 효과 */
.shadow-custom {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.shadow-lg-custom {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
