<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/user}">
<head>
    <title>세션 만료 - 한림공원 QR 체험</title>
    <meta name="description" content="세션이 만료되었습니다.">
</head>
<body>
    <div layout:fragment="content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-md-10">
                    <div class="text-center">
                        <!-- 세션 만료 아이콘 -->
                        <div class="mb-4">
                            <i class="fas fa-clock text-warning" style="font-size: 6rem;"></i>
                        </div>
                        
                        <!-- 메시지 -->
                        <h1 class="h2 mb-3">세션이 만료되었습니다</h1>
                        <p class="lead text-muted mb-4">
                            보안을 위해 일정 시간 후 자동으로 로그아웃됩니다.<br>
                            계속 이용하시려면 다시 로그인해주세요.
                        </p>
                        
                        <!-- 액션 버튼들 -->
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mb-5">
                            <a th:href="@{/user/login}" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>다시 로그인하기
                            </a>
                            <a th:href="@{/}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-home me-2"></i>홈으로 돌아가기
                            </a>
                        </div>
                        
                        <!-- 게스트 이용 안내 -->
                        <div class="alert alert-info border-0" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>로그인 없이도 이용 가능합니다
                            </h6>
                            <p class="mb-3">다음 서비스들은 로그인 없이도 이용하실 수 있습니다:</p>
                            <div class="d-flex flex-wrap gap-2 justify-content-center">
                                <a th:href="@{/quiz}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-question-circle me-1"></i>퀴즈 체험
                                </a>
                                <a th:href="@{/story}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-book me-1"></i>스토리 모드
                                </a>
                                <a th:href="@{/map}" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-map me-1"></i>지도 보기
                                </a>
                            </div>
                        </div>
                        
                        <!-- 세션 관련 안내 -->
                        <div class="mt-5 pt-4 border-top">
                            <h6 class="text-muted mb-3">세션 관리 안내</h6>
                            <div class="row g-3 text-start">
                                <div class="col-md-6">
                                    <div class="card h-100 border-0 bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-shield-alt text-primary me-2"></i>보안 정책
                                            </h6>
                                            <p class="card-text text-muted small">
                                                사용자 정보 보호를 위해 일정 시간 비활성 상태가 지속되면 
                                                자동으로 로그아웃됩니다.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100 border-0 bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-tips text-success me-2"></i>이용 팁
                                            </h6>
                                            <p class="card-text text-muted small">
                                                로그인 상태를 유지하려면 주기적으로 
                                                페이지를 이용해주세요.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
