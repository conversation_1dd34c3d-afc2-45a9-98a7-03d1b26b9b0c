package kr.wayplus.qr_hallimpark.service.user;

import kr.wayplus.qr_hallimpark.common.utils.CustomBcryptPasswordEncoder;
import kr.wayplus.qr_hallimpark.mapper.UserMapper;
import kr.wayplus.qr_hallimpark.model.LoginAttemptLog;
import kr.wayplus.qr_hallimpark.model.LoginUser;
import kr.wayplus.qr_hallimpark.model.LoginUserSession;
import kr.wayplus.qr_hallimpark.model.UserWebLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class UserService implements UserDetailsService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final UserMapper userMapper;

    public UserService(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.debug("Call loadUserByUserName. Username : " + username);
        LoginUser user = userMapper.selectUserByUserid(username);

        List<GrantedAuthority> roles = new ArrayList<>();
        roles.add(new SimpleGrantedAuthority( "ROLE_"+user.getUser_role() ));
        user.setAuthorities(roles);

        if(user == null) throw new UsernameNotFoundException(username);

        logger.debug("User Find. User Has Role : " + user.getAuthorities().toString());
        return user;
    }

    public void updateUserSessionLogout(LoginUserSession loginUserSession) {
        userMapper.updateUserSessionLogout(loginUserSession);
    }

    public void writeUserLoginAttemptLog(LoginAttemptLog attemptLog) {
        userMapper.insertUserLoginAttemptLog(attemptLog);
    }

    public void updateUserWebLog(HashMap<String, String> param) {
        userMapper.updateUserWebLog(param);
    }

    public void updateUserNewTokenId(LoginUser user) {
        userMapper.updateUserNewTokenId(user);
    }

    public void writeUserWebLog(UserWebLog webLog) {
        userMapper.insertUserWebLog(webLog);
    }

    public void writeUserLoginLog(HashMap<String, String> parameterMap) {
        userMapper.insertUserLoginLog(parameterMap);
    }

    public void updateUserLastLoginDate(LoginUser user) {
        userMapper.updateUserLastLoginDate(user);
    }

    public int findUserCountById(String id) {
        return userMapper.selectUserCountById(id);
    }

    public void writeNewUser(LoginUser user) {
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        user.setUser_pass(passwordEncoder.encode(user.getPassword()));
        userMapper.insertNewUser(user);
    }
    
    public void updateUser(LoginUser user) {
    	if(user.getPassword() != null) {
	    	CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
	        user.setUser_pass(passwordEncoder.encode(user.getPassword()));
    	}
    	
    	userMapper.updateUser(user); 
	}

    public LoginUser findUserIdByUserInfo(LoginUser user) {
        return userMapper.selectUserIdByUserInfo(user);
    }

    public LoginUser findUserRePasswordByUserInfo(LoginUser user) {
        return userMapper.selectUserRePasswordByUserInfo(user);
    }

    public void updateUserPasswordByLost(LoginUser user) throws Exception {
        LoginUser storedUser = userMapper.selectUserByUserToken(user);
        if(storedUser == null){
            throw new Exception("사용자를 찾을 수 없습니다.");
        }
        CustomBcryptPasswordEncoder passwordEncoder = new CustomBcryptPasswordEncoder();
        storedUser.setUser_pass(passwordEncoder.encode(user.getUser_pass()));
        userMapper.updateUserPassword(storedUser);
    }
	
}
