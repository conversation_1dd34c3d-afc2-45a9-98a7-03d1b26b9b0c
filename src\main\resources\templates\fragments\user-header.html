<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
</head>
<body>
    <!-- 사용자 헤더 Fragment -->
    <header th:fragment="header" class="header">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <!-- 로고 -->
                <a class="navbar-brand" th:href="@{/}">
                    <img src="/images/logo.png" alt="한림공원" height="40" class="d-inline-block align-text-top">
                    <span class="ms-2 fw-bold text-primary">한림공원 QR 체험</span>
                </a>
                
                <!-- 모바일 메뉴 토글 버튼 -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <!-- 네비게이션 메뉴 -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/}">
                                <i class="fas fa-home me-1"></i>홈
                            </a>
                        </li>
                        <!-- 추후 QR 체험 관련 메뉴 추가 예정 -->
                    </ul>
                    
                    <!-- 우측 메뉴 -->
                    <ul class="navbar-nav">
                        <!-- 사용자 메뉴 -->
                        <li class="nav-item dropdown" sec:authorize="isAuthenticated()">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user me-1"></i>
                                <span sec:authentication="name">사용자</span>
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-user-circle me-2"></i>내 정보
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-trophy me-2"></i>내 점수
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" th:href="@{/user/logout}">
                                    <i class="fas fa-sign-out-alt me-2"></i>로그아웃
                                </a></li>
                            </ul>
                        </li>
                        
                        <!-- 로그인 버튼 (비로그인 시) -->
                        <li class="nav-item" sec:authorize="!isAuthenticated()">
                            <a class="nav-link" th:href="@{/user/login}">
                                <i class="fas fa-sign-in-alt me-1"></i>로그인
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- 알림 메시지 영역 -->
        <div class="container mt-3" th:if="${message != null or errorMessage != null}">
            <div class="alert alert-success alert-dismissible fade show" role="alert" th:if="${message != null}">
                <i class="fas fa-check-circle me-2"></i>
                <span th:text="${message}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" th:if="${errorMessage != null}">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </header>
</body>
</html>
